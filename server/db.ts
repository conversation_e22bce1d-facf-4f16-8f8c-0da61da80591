import { drizzle } from "drizzle-orm/postgres-js";
import postgres from "postgres";
import * as schema from "@shared/schema";

// Create PostgreSQL connection
const connectionString = process.env.DATABASE_URL;

if (!connectionString) {
  throw new Error("DATABASE_URL environment variable is not set");
}

// For direct database interactions
export const client = postgres(connectionString);

// Drizzle ORM client
export const db = drizzle(client, { schema });

// Function to close database connections gracefully
export async function closeDbConnections() {
  try {
    await client.end();
    console.log('Database connections closed successfully');
  } catch (error) {
    console.error('Error closing database connections:', error);
    throw error;
  }
}