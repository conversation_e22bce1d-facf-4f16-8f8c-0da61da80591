import { pgTable, serial, text, timestamp, boolean } from "drizzle-orm/pg-core";
import { createInsertSchema } from "drizzle-zod";
import { z } from "zod";

// User table definition
export const users = pgTable("users", {
  id: serial("id").primaryKey(),
  username: text("username").notNull().unique(),
  email: text("email"),
  password: text("password").notNull(),
  name: text("name"),
  role: text("role"),
  dealership_id: serial("dealership_id"),
  is_verified: text("is_verified"), // Will update this to boolean type once registration works
  verification_token: text("verification_token"),
  reset_token: text("reset_token"),
  reset_token_expiry: timestamp("reset_token_expiry"),
  createdAt: timestamp("created_at").defaultNow(),
  updatedAt: timestamp("updated_at"),
});

// Types and validation schemas
export type User = typeof users.$inferSelect;
export const insertUserSchema = createInsertSchema(users).omit({ id: true });
export type InsertUser = z.infer<typeof insertUserSchema>;
export const reportSchedules = pgTable('report_schedules', {
  active: boolean('active').notNull().default(true),
});